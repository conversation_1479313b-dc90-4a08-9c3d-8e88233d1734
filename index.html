<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />

    <!-- Google Fonts - Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <title>Horizon Carpentry - Brunswick GA Carpenter & Custom Carpentry Services</title>
    <meta
      name="description"
      content="Professional custom carpentry and handyman services in Brunswick, Georgia and surrounding coastal areas. Deck construction, home repairs, bathroom remodeling, and quality craftsmanship. Licensed, insured, and locally trusted."
    />

    <!-- SEO Meta Tags -->
    <meta name="keywords" content="Horizon Carpentry, Brunswick GA carpenter, handyman Brunswick Georgia, deck builder coastal Georgia, home repairs Brunswick, bathroom remodeling Brunswick, construction company coastal Georgia" />
    <meta name="author" content="Horizon Carpentry" />
    <meta name="robots" content="index, follow" />

    <!-- Local Business Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Horizon Carpentry",
      "image": "https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f48942e8ef583415f8e8.webp",
      "description": "Professional custom carpentry and handyman services in Brunswick, Georgia and surrounding coastal areas",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "702 Old CCC Rd",
        "addressLocality": "Brunswick",
        "addressRegion": "GA",
        "postalCode": "31523",
        "addressCountry": "US"
      },
      "telephone": "(*************",
      "email": "<EMAIL>",
      "url": "https://coastalcustomcarpentry.com",
      "openingHours": "Mo-Fr 09:00-18:00",
      "priceRange": "$$",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.7",
        "reviewCount": "12"
      },
      "serviceArea": {
        "@type": "GeoCircle",
        "geoMidpoint": {
          "@type": "GeoCoordinates",
          "latitude": "31.1498",
          "longitude": "-81.3912"
        },
        "geoRadius": "50000"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Carpentry and Handyman Services",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Deck Construction"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Bathroom Remodeling"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Home Repairs"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Handyman Services"
            }
          }
        ]
      }
    }
    </script>

    <!-- TEMPORARILY DISABLED: Chat Widget (Testing if this breaks CSS build) -->
    <!--
    <script>
      // Chat widget loading code temporarily commented out for testing
      console.log('Chat widget temporarily disabled for CSS build testing');
    </script>
    -->

    <!-- Minimal critical CSS -->
    <style>
      /* Basic responsive setup */
      html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        overflow-x: hidden;
      }

      * {
        box-sizing: border-box;
      }

      body {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
      }

      /* Global styles for iframe content */
      iframe[src*="leadconnectorhq.com"] * {
        box-sizing: border-box !important;
      }

      /* Simple FOUC prevention */
      html:not(.loaded) {
        visibility: hidden;
        opacity: 0;
      }

      html.loaded {
        visibility: visible;
        opacity: 1;
        transition: opacity 0.15s ease-in-out;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>

    <script type="module" src="/src/main.tsx"></script>

    <!-- Form Embed Script - Load with error handling -->
    <script>
      // Load HighLevel form script with error handling
      (function() {
        try {
          const script = document.createElement('script');
          script.src = 'https://link.msgsndr.com/js/form_embed.js';
          script.async = true;
          script.onerror = function() {
            console.warn('HighLevel form script failed to load');
          };
          document.head.appendChild(script);
        } catch (e) {
          console.warn('Error loading HighLevel form script:', e);
        }
      })();
    </script>

    <!-- Vapi Voice AI Script - Load with error handling -->
    <script>
      // Load Vapi script with error handling
      (function() {
        try {
          const script = document.createElement('script');
          script.src = 'https://unpkg.com/@vapi-ai/client-sdk-react/dist/embed/widget.umd.js';
          script.async = true;
          script.type = 'text/javascript';
          script.onerror = function() {
            console.warn('Vapi Voice AI script failed to load');
          };
          document.head.appendChild(script);
        } catch (e) {
          console.warn('Error loading Vapi Voice AI script:', e);
        }
      })();
    </script>

    <!-- Custom script to handle form styling -->
    <script>
      // Function to inject custom CSS into HighLevel iframes
      function injectHighLevelStyles() {
        const iframes = document.querySelectorAll(
          'iframe[src*="leadconnectorhq.com"]'
        );

        iframes.forEach((iframe) => {
          iframe.addEventListener("load", function () {
            try {
              // Try to access iframe content (may be blocked by CORS)
              const iframeDoc =
                iframe.contentDocument || iframe.contentWindow.document;

              if (iframeDoc) {
                const customStyles = `
                  <style>
                    /* Reset spacing on HighLevel's wrapper DIVs */
                    .field-container,
                    .field-container > div,
                    .col-12 {
                      padding: 0 !important;
                      margin: 2px 0 !important;
                    }

                    /* Hide unwanted, auto-generated address fields */
                    .non-address-elements,
                    #address,
                    #form-address {
                      display: none !important;
                    }

                    /* Hide default field labels if you use placeholders instead */
                    .form-builder--item label {
                      display: none !important;
                    }

                    .p {
                      font-size: 7px !important;
                    }

                    .extra-top-padding {
                      margin-top: 0 !important;
                    }

                    .fields-container {
                      padding-top: 0 !important;
                      padding-bottom: 0 !important;
                      margin-top: 0 !important;
                      margin-bottom: 0 !important;
                    }
                  </style>
                `;

                iframeDoc.head.insertAdjacentHTML("beforeend", customStyles);
              }
            } catch (e) {
              // CORS restriction - styles will need to be applied in HighLevel directly
              console.log("Cannot inject styles due to CORS policy");
            }
          });
        });
      }

      // Run when DOM is loaded
      document.addEventListener("DOMContentLoaded", function () {
        // Initial injection
        setTimeout(injectHighLevelStyles, 1000);

        // Re-inject periodically in case forms load dynamically
        setInterval(injectHighLevelStyles, 3000);
      });
    </script>
  </body>
</html>
